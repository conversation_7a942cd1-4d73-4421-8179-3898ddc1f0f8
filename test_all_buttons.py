#!/usr/bin/env python3
"""
Comprehensive Button Test Program
Tests ALL position and rotation buttons for correct behavior
"""

import sys
import time
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton, QTextEdit
from PyQt5.QtCore import QTimer
from step_viewer_tdk_modular import StepViewerTDK

class ButtonTester(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Button Test Program")
        self.setGeometry(100, 100, 800, 600)
        
        # Create main widget
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        layout = QVBoxLayout(main_widget)
        
        # Status label
        self.status_label = QLabel("Starting button tests...")
        layout.addWidget(self.status_label)
        
        # Results text area
        self.results_text = QTextEdit()
        self.results_text.setReadOnly(True)
        layout.addWidget(self.results_text)
        
        # Create the actual viewer
        self.viewer = StepViewerTDK()
        self.viewer.show()
        
        # Test parameters
        self.test_step = 0
        self.test_results = []
        self.position_increment = 1.0  # 1mm
        self.rotation_increment = 15.0  # 15 degrees
        
        # Start tests after a delay
        self.timer = QTimer()
        self.timer.timeout.connect(self.run_next_test)
        self.timer.start(3000)  # Start after 3 seconds
        
    def log_result(self, message):
        """Log a test result"""
        print(message)
        self.results_text.append(message)
        self.test_results.append(message)
        
    def get_current_values(self, viewer_name):
        """Get current position and rotation values from viewer"""
        if viewer_name == "TOP":
            pos = getattr(self.viewer, 'movement_delta_left', {'x': 0, 'y': 0, 'z': 0})
            rot = getattr(self.viewer, 'current_rot_left', {'x': 0, 'y': 0, 'z': 0})
            orig_pos = getattr(self.viewer, 'orig_pos_left', {'x': 0, 'y': 0, 'z': 0})
        else:  # BOTTOM
            pos = getattr(self.viewer, 'movement_delta_right', {'x': 0, 'y': 0, 'z': 0})
            rot = getattr(self.viewer, 'current_rot_right', {'x': 0, 'y': 0, 'z': 0})
            orig_pos = getattr(self.viewer, 'orig_pos_right', {'x': 0, 'y': 0, 'z': 0})
        
        # Calculate actual position (original - movement_delta due to coordinate system)
        actual_pos = {
            'x': orig_pos['x'] - pos['x'],
            'y': orig_pos['y'] - pos['y'], 
            'z': orig_pos['z'] - pos['z']
        }
        
        return actual_pos, rot
        
    def test_button(self, viewer_name, button_type, axis, expected_change, button_name):
        """Test a specific button"""
        self.log_result(f"\n🔧 TESTING {viewer_name} {button_name} button:")
        
        # Set active viewer
        if viewer_name == "TOP":
            self.viewer.active_viewer = "top"
        else:
            self.viewer.active_viewer = "bottom"
            
        # Get values before button press
        pos_before, rot_before = self.get_current_values(viewer_name)
        self.log_result(f"   Before: pos={pos_before}, rot={rot_before}")
        
        # Press the button
        if button_type == "position":
            self.viewer.move_shape(axis, expected_change)
        else:  # rotation
            self.viewer.rotate_shape(axis, expected_change)
            
        # Allow time for updates
        QApplication.processEvents()
        time.sleep(0.1)
        
        # Get values after button press
        pos_after, rot_after = self.get_current_values(viewer_name)
        self.log_result(f"   After:  pos={pos_after}, rot={rot_after}")
        
        # Check the change
        if button_type == "position":
            actual_change = pos_after[axis] - pos_before[axis]
            expected_text = f"position {axis.upper()}"
        else:
            actual_change = rot_after[axis] - rot_before[axis]
            expected_text = f"rotation {axis.upper()}"
            
        self.log_result(f"   Expected change: {expected_change}")
        self.log_result(f"   Actual change: {actual_change}")
        
        # Determine result
        tolerance = 0.01
        if abs(actual_change - expected_change) < tolerance:
            result = "✅ PASS"
        else:
            result = "❌ FAIL"
            
        self.log_result(f"   Result: {result}")
        return result == "✅ PASS"
        
    def run_next_test(self):
        """Run the next test in sequence"""
        
        if self.test_step == 0:
            self.log_result("=" * 60)
            self.log_result("🚀 STARTING COMPREHENSIVE BUTTON TESTS")
            self.log_result("=" * 60)
            self.log_result("Loading STEP file first...")
            
            # Load a STEP file
            try:
                self.viewer.load_step_file_direct("test.step")
                QApplication.processEvents()
                time.sleep(2)  # Wait for loading
                self.log_result("✅ STEP file loaded successfully")
            except Exception as e:
                self.log_result(f"❌ Failed to load STEP file: {e}")
                
        elif self.test_step == 1:
            self.log_result("\n📋 TESTING TOP VIEWER POSITION BUTTONS:")
            self.test_button("TOP", "position", "x", +self.position_increment, "X+")
            
        elif self.test_step == 2:
            self.test_button("TOP", "position", "x", -self.position_increment, "X-")
            
        elif self.test_step == 3:
            self.test_button("TOP", "position", "y", +self.position_increment, "Y+")
            
        elif self.test_step == 4:
            self.test_button("TOP", "position", "y", -self.position_increment, "Y-")
            
        elif self.test_step == 5:
            self.test_button("TOP", "position", "z", +self.position_increment, "Z+")
            
        elif self.test_step == 6:
            self.test_button("TOP", "position", "z", -self.position_increment, "Z-")
            
        elif self.test_step == 7:
            self.log_result("\n📋 TESTING TOP VIEWER ROTATION BUTTONS:")
            self.test_button("TOP", "rotation", "x", +self.rotation_increment, "X+ Rotation")
            
        elif self.test_step == 8:
            self.test_button("TOP", "rotation", "x", -self.rotation_increment, "X- Rotation")
            
        elif self.test_step == 9:
            self.test_button("TOP", "rotation", "y", +self.rotation_increment, "Y+ Rotation")
            
        elif self.test_step == 10:
            self.test_button("TOP", "rotation", "y", -self.rotation_increment, "Y- Rotation")
            
        elif self.test_step == 11:
            self.test_button("TOP", "rotation", "z", +self.rotation_increment, "Z+ Rotation")
            
        elif self.test_step == 12:
            self.test_button("TOP", "rotation", "z", -self.rotation_increment, "Z- Rotation")
            
        elif self.test_step == 13:
            self.log_result("\n📋 TESTING BOTTOM VIEWER POSITION BUTTONS:")
            self.test_button("BOTTOM", "position", "x", +self.position_increment, "X+")
            
        elif self.test_step == 14:
            self.test_button("BOTTOM", "position", "x", -self.position_increment, "X-")
            
        elif self.test_step == 15:
            self.test_button("BOTTOM", "position", "y", +self.position_increment, "Y+")
            
        elif self.test_step == 16:
            self.test_button("BOTTOM", "position", "y", -self.position_increment, "Y-")
            
        elif self.test_step == 17:
            self.test_button("BOTTOM", "position", "z", +self.position_increment, "Z+")
            
        elif self.test_step == 18:
            self.test_button("BOTTOM", "position", "z", -self.position_increment, "Z-")
            
        elif self.test_step == 19:
            self.log_result("\n📋 TESTING BOTTOM VIEWER ROTATION BUTTONS:")
            self.test_button("BOTTOM", "rotation", "x", +self.rotation_increment, "X+ Rotation")
            
        elif self.test_step == 20:
            self.test_button("BOTTOM", "rotation", "x", -self.rotation_increment, "X- Rotation")
            
        elif self.test_step == 21:
            self.test_button("BOTTOM", "rotation", "y", +self.rotation_increment, "Y+ Rotation")
            
        elif self.test_step == 22:
            self.test_button("BOTTOM", "rotation", "y", -self.rotation_increment, "Y- Rotation")
            
        elif self.test_step == 23:
            self.test_button("BOTTOM", "rotation", "z", +self.rotation_increment, "Z+ Rotation")
            
        elif self.test_step == 24:
            self.test_button("BOTTOM", "rotation", "z", -self.rotation_increment, "Z- Rotation")
            
        elif self.test_step == 25:
            self.log_result("\n📋 TESTING RESET FUNCTIONALITY:")
            
            # Test TOP reset
            self.viewer.active_viewer = "top"
            pos_before, rot_before = self.get_current_values("TOP")
            self.log_result(f"TOP before reset: pos={pos_before}, rot={rot_before}")
            
            self.viewer.reset_to_original()
            QApplication.processEvents()
            time.sleep(0.5)
            
            pos_after, rot_after = self.get_current_values("TOP")
            self.log_result(f"TOP after reset: pos={pos_after}, rot={rot_after}")
            
        elif self.test_step == 26:
            # Test BOTTOM reset
            self.viewer.active_viewer = "bottom"
            pos_before, rot_before = self.get_current_values("BOTTOM")
            self.log_result(f"BOTTOM before reset: pos={pos_before}, rot={rot_before}")
            
            self.viewer.reset_to_original()
            QApplication.processEvents()
            time.sleep(0.5)
            
            pos_after, rot_after = self.get_current_values("BOTTOM")
            self.log_result(f"BOTTOM after reset: pos={pos_after}, rot={rot_after}")
            
            # Check if BOTTOM Z axis shows 9 from STEP file
            orig_rot_right = getattr(self.viewer, 'orig_rot_right', {'z': 0})
            self.log_result(f"BOTTOM orig_rot_right Z: {orig_rot_right.get('z', 'NOT SET')}")
            if orig_rot_right.get('z', 0) == 9.0:
                self.log_result("✅ BOTTOM Z axis correctly shows 9 from STEP file")
            else:
                self.log_result("❌ BOTTOM Z axis should show 9 from STEP file")
                
        else:
            # Test complete
            self.log_result("\n" + "=" * 60)
            self.log_result("🏁 ALL BUTTON TESTS COMPLETE")
            self.log_result("=" * 60)
            
            # Count results
            passes = sum(1 for result in self.test_results if "✅ PASS" in result)
            fails = sum(1 for result in self.test_results if "❌ FAIL" in result)
            
            self.log_result(f"📊 SUMMARY: {passes} PASSED, {fails} FAILED")
            
            if fails == 0:
                self.log_result("🎉 ALL TESTS PASSED!")
            else:
                self.log_result("⚠️  SOME TESTS FAILED - CHECK RESULTS ABOVE")
                
            self.timer.stop()
            return
            
        self.test_step += 1

if __name__ == "__main__":
    app = QApplication(sys.argv)
    tester = ButtonTester()
    tester.show()
    sys.exit(app.exec_())
